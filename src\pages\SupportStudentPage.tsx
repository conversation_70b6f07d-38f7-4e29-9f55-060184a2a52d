import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { toast } from 'react-toastify';
import paymentService from '../services/paymentService';
import supporterService from '../services/supporterService';
import supporterCampaignService from '../services/supporterCampaignService';
import LoadingSpinner from '../components/LoadingSpinner';
import StatusPage from '../components/StatusPage';
import SupporterHeader from '../components/SupporterHeader';
import Breadcrumb from '../components/Breadcrumb';
import { useSupporterAuth } from "../context/SupporterAuthContext";
import { getBasePathWithSlash } from '../utils/basePath';
import { getBreadcrumbItems } from '../utils/breadcrumbUtils';


declare global {
  interface Window {
    Razorpay: any;
  }
}

interface SupportStudentPageProps {
  // Add any additional props if needed
}

const SupportStudentPage: React.FC<SupportStudentPageProps> = () => {
  const { studentId } = useParams();
  const navigate = useNavigate();
  const { isAuthenticated } = useSupporterAuth();
  const [selectedAmount, setSelectedAmount] = useState<string>('');
  const [customAmount, setCustomAmount] = useState<string>('');
  const [campaign, setCampaign] = useState<any | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [status, setStatus] = useState<'idle' | 'success' | 'error' | 'pending'>('idle');
  const [errorDetails, setErrorDetails] = useState<{message?: string} | null>(null);

  const predefinedAmounts = [1000, 2000, 5000, 10000];

  useEffect(() => {
    // Check if there's a saved donation amount from before login
    const savedAmount = sessionStorage.getItem('donation_amount');
    if (savedAmount) {
      // Convert to number to determine if it matches a predefined amount
      const numAmount = Number(savedAmount);
      if (predefinedAmounts.includes(numAmount)) {
        setSelectedAmount(savedAmount);
      } else {
        // Format with commas for custom amount
        setCustomAmount(numAmount.toLocaleString('en-IN'));
      }
      // Clear the stored amount to prevent future unwanted pre-fills
      sessionStorage.removeItem('donation_amount');
    }
    
    if (!studentId) return;

    let isMounted = true;
    let retryCount = 0;
    const maxRetries = 2;

    const fetchCampaign = async () => {
      if (!isMounted) return;
      
      setLoading(true);
      try {
        console.log(`Fetching campaign data for ID: ${studentId} (attempt ${retryCount + 1})`);
        
        // Try to fetch using the supporterCampaignService first (direct API call)
        const data = await supporterCampaignService.getCampaignById(studentId);
        console.log('Campaign data from supporterCampaignService:', data);
        
        if (data && isMounted) {
          setCampaign(data);
        } else if (isMounted) {
          throw new Error('Empty response from supporterCampaignService');
        }
      } catch (error) {
        console.error('Error fetching campaign with supporterCampaignService:', error);
        
        if (!isMounted) return;
        
        // Fallback to supporterService if the first attempt fails
        try {
          console.log('Trying fallback with supporterService...');
          const fallbackData = await supporterService.getCampaignById(studentId);
          console.log('Campaign data from supporterService fallback:', fallbackData);
          
          if (fallbackData && isMounted) {
            setCampaign(fallbackData);
          } else if (isMounted) {
            throw new Error('Empty response from supporterService');
          }
        } catch (fallbackError) {
          if (isMounted) {
            console.error('Error fetching campaign with fallback service:', fallbackError);

            // Retry logic
            if (retryCount < maxRetries) {
              retryCount++;
              console.log(`Retrying fetch (${retryCount}/${maxRetries})...`);
              setTimeout(fetchCampaign, 1000); // Wait 1 second before retrying
            } else {
              setErrorDetails({ message: 'Failed to load campaign information' });
              setStatus('error');
            }
          }
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    // Execute fetch immediately
    fetchCampaign();
    
    // Cleanup function to prevent state updates after unmount
    return () => {
      isMounted = false;
    };
  }, [studentId]);

  const handleAmountSelect = (amount: number) => {
    setSelectedAmount(amount.toString());
    setCustomAmount('');
  };

  const handleCustomAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Remove commas and non-numeric characters, prevent negative values
    const rawValue = e.target.value.replace(/[^0-9]/g, '');
    
    // Don't update if empty
    if (!rawValue) {
      setCustomAmount('');
      setSelectedAmount('');
      return;
    }
    
    // Convert to number and format with commas
    const numValue = parseInt(rawValue, 10);
    setCustomAmount(numValue.toLocaleString('en-IN'));
    setSelectedAmount('');
  };

  const loadRazorpay = () => {
    return new Promise<boolean>((resolve) => {
      const script = document.createElement('script');
      script.src = 'https://checkout.razorpay.com/v1/checkout.js';
      script.onload = () => {
        resolve(true);
      };
      script.onerror = () => {
        resolve(false);
      };
      document.body.appendChild(script);
    });
  };

  const initializeRazorpayPayment = async (finalAmount: string) => {
    try {
      const res = await loadRazorpay();
      if (!res) {
        toast.error('Razorpay SDK failed to load');
        return;
      }
      if (!studentId) {
        toast.error('No student selected');
        return;
      }

      const order = await paymentService.createOrder(String(studentId), Number(finalAmount));

      const options = {
        key: order.key || import.meta.env.VITE_RAZORPAY_KEY_ID,
        amount: order.amount,
        currency: 'INR',
        name: 'EDU-FUND',
        description: 'Supporting student education',
        image: import.meta.env.VITE_RAZORPAY_LOGO_URL,
        order_id: order.orderId,
        handler: async function (response: any) {
          try {
            // Check if we have all required payment data
            if (response.razorpay_payment_id && response.razorpay_order_id && response.razorpay_signature) {
              // We have complete payment data - verify with backend
              try {
                const result = await paymentService.verifyPayment(response);
                if (result.success) {
                  // Backend confirmed payment is successful
                  setStatus('success');
                } else {
                  // Backend says payment failed despite having payment ID
                  console.warn('Payment verification failed:', result.message);
                  setErrorDetails({ message: result.message || 'Payment verification failed' });
                  setStatus('error');
                }
              } catch (verifyErr: any) {
                // Backend verification failed - could be network issue
                console.error('Payment verification error:', verifyErr);
                // Show pending status and let user check manually
                setErrorDetails({ message: 'Payment status unclear. Please check your payment history or contact support.' });
                setStatus('pending');
              }
            } else if (response.razorpay_payment_id) {
              // We have payment ID but missing other data - incomplete payment
              console.log('Incomplete payment data received');
              setErrorDetails({ message: 'Payment is incomplete. Please try again or contact support.' });
              setStatus('pending');
            } else {
              // No payment ID at all - payment not initiated properly
              console.log('No payment ID received - payment not completed');
              setErrorDetails({ message: 'Payment was not completed. Please try again.' });
              setStatus('error');
            }
          } catch (err: any) {
            console.error('Payment processing failed', err);
            const apiMessage = err.response?.data?.message || '';
            const apiData = err.response?.data?.data || {};
            let errorMessage = apiMessage || 'Payment processing failed';
            if (Object.keys(apiData).length > 0) {
              const validationErrors = Object.entries(apiData)
                .map(([field, error]) => `• ${field}: ${error}`)
                .join('\n');
              errorMessage = apiMessage + '\n\n' + validationErrors;
            }
            setErrorDetails({ message: errorMessage });
            setStatus('error');
          }
        },
        prefill: {
          name: 'Supporter Name',
          email: '<EMAIL>',
          contact: '9999999999',
        },
        notes: {
          studentId: studentId ?? '',
        },
        theme: {
          color: '#2563EB',
        },
        modal: {
          ondismiss: function () {
            // handle dismissal
          },
        },
      } as any;

      const paymentObject = new window.Razorpay(options);
      // Show an error screen when the payment fails
      paymentObject.on('payment.failed', (resp: any) => {
        const description = resp?.error?.description || 'Payment failed';
        setErrorDetails({ message: description });
        setStatus('error');
      });
      paymentObject.open();
    } catch (error: any) {
      console.error('Payment initialization error:', error);
      const apiMessage = error.response?.data?.message || '';
      const apiData = error.response?.data?.data || {};
      let errorMessage = apiMessage;
      if (Object.keys(apiData).length > 0) {
        const validationErrors = Object.entries(apiData)
          .map(([field, error]) => `• ${field}: ${error}`)
          .join('\n');
        errorMessage = apiMessage + '\n\n' + validationErrors;
      }
      setErrorDetails({ message: errorMessage });
      setStatus('error');
    }
  };

  const handleProceedToPayment = () => {
    // Get the amount and remove commas for processing
    const finalAmount = selectedAmount || customAmount.replace(/,/g, '');

    if (!finalAmount || Number(finalAmount) <= 0) {
      toast.error('Please select or enter a valid amount');
      return;
    }

    if (!isAuthenticated) {
      // Save the donation amount to redirect back after login
      sessionStorage.setItem('donation_amount', finalAmount);
      
      // Store the current pathname without the base path to redirect back after login
      const basePath = import.meta.env.VITE_BASE_PATH || '';
      let redirectPath = window.location.pathname;
      
      // Remove the base path if it's at the beginning of the pathname
      if (basePath && redirectPath.startsWith(basePath)) {
        redirectPath = redirectPath.substring(basePath.length);
      }
      
      sessionStorage.setItem('login_redirect', redirectPath);
      toast.info('Please sign in to complete your donation');
      navigate('/supporter-login');
      return;
    }

    initializeRazorpayPayment(finalAmount);
  };

  // Helper function to safely render campaign data regardless of structure
  const renderCampaignInfo = () => {
    if (!campaign) return null;
    
    // Log the campaign structure to help debug
    console.log('Campaign structure in render:', campaign);
    
    // Handle different possible data structures
    const campaignTitle = campaign.title || (campaign.campaign && campaign.campaign.name) || 'Campaign';
    const studentName = campaign.student?.name || campaign.name || 'Student';
    const institution = campaign.student?.institutionName || campaign.institution || '';
    const course = campaign.student?.course || campaign.course || '';
    
    // Handle different target/raised amount structures
    const targetAmount = campaign.target || 
                        (campaign.campaign && campaign.campaign.targetAmount) || 
                        0;
    const raisedAmount = campaign.raised || 
                        (campaign.campaign && campaign.campaign.raisedAmount) || 
                        0;
    const supporters = campaign.supporters || 
                      (campaign.campaign && campaign.campaign.supporters) || 
                      0;
    
    return (
      <div className="mb-6 space-y-2">
  <h2 className="text-3xl font-extrabold text-gray-800">{campaignTitle}</h2>
  
  <p className="text-lg text-gray-700 font-medium">{studentName}</p>
  
  <p className="text-sm text-gray-700 font-bold italic">
    {institution} {course ? `- ${course}` : ''}
  </p>

  <div className="flex flex-wrap gap-4 text-sm text-gray-700 mt-2">
    <div className="bg-gray-100 rounded-md px-3 py-1">
      <span className="font-semibold">Target:</span> ₹{targetAmount.toLocaleString()}
    </div>
    <div className="bg-gray-100 rounded-md px-3 py-1">
      <span className="font-semibold">Raised:</span> ₹{raisedAmount.toLocaleString()}
    </div>
    <div className="bg-gray-100 rounded-md px-3 py-1">
      <span className="font-semibold">Supporters:</span> {supporters}
    </div>
  </div>
</div>

    
    );
  };

  if (status === 'success') {
    return (
      <StatusPage
        type="success"
        title="Payment Successful"
        message="Thank you for your generous support! Your contribution will help the student achieve their educational goals."
        actionText="View Donations"
        backUrl={`${getBasePathWithSlash()}supporters?tab=donations`}
      />
    );
  }

  if (status === 'pending') {
    return (
      <StatusPage
        type="pending"
        title="Payment Processing"
        message={errorDetails?.message || 'Your payment is being processed. This may take a few moments. Please do not close this page.'}
        actionText="Check Status"
        onAction={() => { setStatus('idle'); setErrorDetails(null); }}
        secondaryActionText="Cancel"
        onSecondaryAction={() => { setStatus('idle'); setErrorDetails(null); }}
      />
    );
  }

  if (status === 'error') {
    return (
      <StatusPage
        type="error"
        title="Error"
        message={errorDetails?.message || ""}
        actionText="Try Again"
        onAction={() => {
          setStatus('idle');
          setErrorDetails(null);
        }}
      />
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      <SupporterHeader />
      <div className="container mx-auto px-4 py-2">
        <Breadcrumb
          items={getBreadcrumbItems('support-student', {
            studentName: campaign?.name || 'Student',
            studentId: studentId
          })}
        />
      </div>
      <div className="flex-grow flex items-center justify-center p-4">
        <div className="max-w-2xl w-full p-6 bg-white rounded-lg shadow-lg">
          {loading ? (
            <div className="flex justify-center items-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
              <p className="ml-3 text-lg text-gray-600">Loading campaign details...</p>
            </div>
          ) : campaign ? (
            renderCampaignInfo()
          ) : (
            <div className="text-center py-6 text-red-500">
              Campaign information could not be loaded. Please try again.
            </div>
          )}

          <h2 className="text-2xl font-bold mb-6">Support Student</h2>
          
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-4">Select Amount (₹)</h3>
            <div className="grid grid-cols-2 gap-4 mb-6">
              {predefinedAmounts.map((amount) => (
                <button
                  key={amount}
                  onClick={() => handleAmountSelect(amount)}
                  className={`p-4 text-center rounded-lg border ${
                    selectedAmount === amount.toString()
                      ? 'bg-blue-600 text-white border-blue-600'
                      : 'border-gray-300 hover:border-blue-600'
                  }`}
                >
                  ₹{amount.toLocaleString()}
                </button>
              ))}
            </div>
          </div>

          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-2">Or Enter Custom Amount</h3>
            <div className="relative">
              <span className="absolute left-3 top-1/2 transform -translate-y-1/2">₹</span>
              <input
                type="text"
                value={customAmount}
                onChange={handleCustomAmountChange}
                placeholder="Enter amount"
                className="w-full p-3 pl-8 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                inputMode="numeric"
              />
            </div>
          </div>

          <button
            onClick={handleProceedToPayment}
            disabled={!selectedAmount && !customAmount}
            className="w-full bg-blue-600 text-white py-3 rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
          >
            Proceed to Payment
          </button>
        </div>
      </div>
    </div>
  );
};

export default SupportStudentPage; 