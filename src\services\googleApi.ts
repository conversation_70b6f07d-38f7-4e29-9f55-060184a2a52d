import axios from 'axios';
import { backendUrl } from '../config/api';

// In-memory token storage (secure, not visible in browser storage)
let currentGoogleToken: string | null = null;

// Storage key for Google token (using sessionStorage for security)
const GOOGLE_TOKEN_KEY = 'google_auth_token';

// Function to set the Google token securely (called after successful login)
export const setGoogleToken = (token: string) => {
  currentGoogleToken = token;
  sessionStorage.setItem(GOOGLE_TOKEN_KEY, token);
  console.log('Google token set securely in memory and sessionStorage');
};

// Function to get the current Google token
export const getGoogleToken = (): string | null => {
  if (currentGoogleToken) return currentGoogleToken;

  const storedToken = sessionStorage.getItem(GOOGLE_TOKEN_KEY);
  if (storedToken) {
    try {
      const payload = JSON.parse(atob(storedToken.split('.')[1]));
      if (payload.exp * 1000 > Date.now()) {
        currentGoogleToken = storedToken;
        console.log('Google token restored from sessionStorage');
        return storedToken;
      } else {
        console.log('Stored Google token is expired, clearing');
        sessionStorage.removeItem(GOOGLE_TOKEN_KEY);
      }
    } catch (e) {
      console.error('Error validating stored token:', e);
      sessionStorage.removeItem(GOOGLE_TOKEN_KEY);
    }
  }

  return null;
};

// Function to clear the Google token (called on logout)
export const clearGoogleToken = () => {
  currentGoogleToken = null;
  sessionStorage.removeItem(GOOGLE_TOKEN_KEY);
  console.log('Google token cleared from memory and sessionStorage');
};

// Function to check if user has a valid Google token
export const hasGoogleToken = (): boolean => {
  return getGoogleToken() !== null;
};

// Function to get current user's email from Google token
export const getCurrentUserEmail = (): string | null => {
  const token = getGoogleToken();
  if (!token) return null;

  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    return payload.email || null;
  } catch (e) {
    console.error('Error parsing token for email:', e);
    return null;
  }
};

// ✅ SINGLE COPY of getCurrentUserName
export const getCurrentUserName = (): string | null => {
  const token = getGoogleToken();
  if (token) {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      if (payload.name) return payload.name;
    } catch (e) {
      console.error('Error parsing token for name:', e);
    }
  }

  const googleName = localStorage.getItem('google_auth_name');
  if (googleName) return googleName;

  try {
    const userStr = localStorage.getItem('user') || sessionStorage.getItem('user');
    if (userStr) {
      const user = JSON.parse(userStr);
      if (user.name) return user.name;
    }
  } catch (e) {
    console.error('Error parsing user data for name:', e);
  }

  return null;
};

// ✅ SINGLE COPY of isCurrentUserAuthor
export const isCurrentUserAuthor = (authorName: string | null | undefined): boolean => {
  if (!authorName) return false;
  
  console.log('Checking if current user is author of:', authorName);

  const currentUserName = getCurrentUserName();
  console.log('Current user name:', currentUserName);
  if (currentUserName && authorName.toLowerCase() === currentUserName.toLowerCase()) {
    console.log('Name match found');
    return true;
  }

  const orgName = localStorage.getItem('org_name');
  console.log('Org name from localStorage:', orgName);
  if (orgName && authorName.toLowerCase() === orgName.toLowerCase()) {
    console.log('Org name match found');
    return true;
  }

  const idToken = localStorage.getItem('id_token') || sessionStorage.getItem('id_token');
  if (idToken) {
    try {
      const payload = JSON.parse(atob(idToken.split('.')[1]));
      console.log('ID token payload:', payload);
      if (payload.name && authorName.toLowerCase() === payload.name.toLowerCase()) {
        console.log('ID token name match found');
        return true;
      }
    } catch (e) {
      console.error('Error parsing ID token:', e);
    }
  }

  // Check for role-based authorization - only for SPONSOR role
  if (authorName.includes('SPONSOR')) {
    console.log('Role-based match (SPONSOR) found');
    return true;
  }

  console.log('No match found for author:', authorName);
  return false;
};

const googleApi = axios.create({
  baseURL: backendUrl,
  responseType: 'json',
});

// Request interceptor to attach the Google token
googleApi.interceptors.request.use(
  (config) => {
    const token = getGoogleToken();
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;

      try {
        const tokenParts = token.split('.');
        if (tokenParts.length === 3) {
          const payload = JSON.parse(atob(tokenParts[1]));
          console.log('🔍 Google Token Debug:', {
            aud: payload.aud,
            iss: payload.iss,
            exp: payload.exp,
            iat: payload.iat,
            email: payload.email,
            tokenLength: token.length,
            url: `${config.baseURL}${config.url}`,
            source: currentGoogleToken ? 'memory' : 'sessionStorage'
          });
        }
      } catch (e) {
        console.error('Error parsing token for debug:', e);
      }
    } else {
      console.warn('No Google token available for API request');
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor to handle token expiration and server errors
googleApi.interceptors.response.use(
  (response) => response,
  (error) => {
    // Log all API errors with detailed information
    console.error('GoogleApi Error:', {
      url: error.config?.url,
      method: error.config?.method,
      status: error.response?.status,
      data: error.response?.data
    });
    
    if (error.response?.status === 401) {
      console.warn('Google token may be expired, clearing from memory');
      clearGoogleToken();

      const currentPath = window.location.pathname.toLowerCase();
      const isPublicCampaignPage = currentPath.includes('/campaign/') && !currentPath.includes('student-campaign') && !currentPath.includes('institution-campaign');

      if (!isPublicCampaignPage) {
        const basePath = window.location.pathname.includes(`${import.meta.env.VITE_BASE_PATH}`) ? `${import.meta.env.VITE_BASE_PATH}/` : '/';

        let loginRoute = 'institution-login';
        if (currentPath.includes('student')) {
          loginRoute = 'student-login';
        } else if (currentPath.includes('supporter')) {
          loginRoute = 'supporter-login';
        }

        window.location.href = basePath + loginRoute;
      }
    }
    
    // Always reject the promise with the error so it can be caught and handled properly
    return Promise.reject(error);
  }
);

export default googleApi;
