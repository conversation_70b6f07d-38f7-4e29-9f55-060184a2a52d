import React, { useEffect, useState } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate, Link } from 'react-router-dom';
import {
  ChevronLeft,
  Share2,
  Calendar,
  Users,
  Target,
  TrendingUp,
  FileText,
  School,
  Book,
  Mail,
  Phone,
  Clock,
  AlertCircle,
  GraduationCap,
  X,
  QrCode,
  LogOut,
  CheckCircle,
  XCircle,
  Trash2
} from 'lucide-react';
import { toast } from 'react-toastify';
import { useSupporterAuth } from '../context/SupporterAuthContext';
import Breadcrumb from '../components/Breadcrumb';
import { getBreadcrumbItems } from '../utils/breadcrumbUtils';
import { formatDateTime } from '../utils/dateUtils';
import supporterService from '../services/supporterService';
import feedbackService from '../services/feedbackService';
import { getGoogleToken, getCurrentUserEmail, getCurrentUserName, isCurrentUserAuthor } from '../services/googleApi';
import StatusPage from '../components/StatusPage';
import paymentService from '../services/paymentService';
import SupporterHeader from '../components/SupporterHeader';
import LanguageDropdown from '../components/LanguageDropdown';
import translationService, { SupportedLanguage } from '../services/translationService';


interface CampaignDetails {
  id: number;
  studentId: string;
  name: string;
  institution: string;
  institutionLogo?: string;
  course: string;
  year: string;
  email: string;
  phone: string;
  imageUrl: string;
  showSupportersToOthers?: boolean;
  supportersList?: Array<{
    id: string;
    name: string;
    amount: number;
    date: string;
    message?: string;
  }>;
  campaign: {
    name: string;
    story: string;
    targetAmount: number;
    raisedAmount: number;
    startDate: string;
    endDate: string;
    status: 'active' | 'completed' | 'upcoming';
    supporters: number;
    documents: {
      name: string;
      file: string;
    }[];
    updates: {
      date: string;
      title: string;
      content: string;
    }[];
    milestones: {
      title: string;
      status: 'completed' | 'in-progress' | 'pending';
      dueDate: string;
    }[];
  };
}




export function CampaignDetailsPage() {
  const { id } = useParams();
  const navigate = useNavigate();
  const [campaign, setCampaign] = useState<CampaignDetails | null>(null);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [showShareModal, setShowShareModal] = useState(false);
  const [showQRModal, setShowQRModal] = useState(false);
  const [donationAmount, setDonationAmount] = useState('');
  const [showReceiptModal, setShowReceiptModal] = useState(false);
  const [paymentStatus, setPaymentStatus] = useState<'success' | 'failed' | null>(null);
  const [wordsOfSupport, setWordsOfSupport] = useState('');
  const [replyText, setReplyText] = useState('');
  const [replyingTo, setReplyingTo] = useState<string | null>(null);
  const [supportMessages, setSupportMessages] = useState<Array<{id: string, name: string, email: string, message: string, date: string, replies?: Array<{id: string, name: string, email: string, message: string, date: string}>}>>([]);
  const [status, setStatus] = useState<'idle' | 'success' | 'error' | 'pending'>('idle');
  const [errorDetails, setErrorDetails] = useState<{ message?: string } | null>(null);
  const [selectedLanguage, setSelectedLanguage] = useState<SupportedLanguage>('en');
  const [translatedCampaign, setTranslatedCampaign] = useState<CampaignDetails | null>(null);
  const [uiLabels, setUiLabels] = useState<Record<string, string>>({});
  const [translatedMessages, setTranslatedMessages] = useState<any[]>([]);
  const { isAuthenticated, logout } = useSupporterAuth();

  const handleSignOut = () => {
    // First clear all authentication data
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    localStorage.removeItem('id_token');
    sessionStorage.removeItem('token');
    sessionStorage.removeItem('user');
    sessionStorage.removeItem('id_token');

    // Then use the logout function to update the context state
    logout('/supporter-login');

    // Show a success message
    toast.success('Successfully signed out');

    // Force a page reload to clear any remaining state
    setTimeout(() => {
      window.location.href = `${import.meta.env.VITE_BASE_PATH}/supporter-login`;
    }, 100);
  };

  const addReplyToMessages = (
    messages: Array<any>,
    parentId: string,
    reply: any
  ): Array<any> => {
    return messages.map(msg => {
      if (msg.id === parentId) {
        return { ...msg, replies: [...(msg.replies || []), reply] };
      }
      if (msg.replies && msg.replies.length > 0) {
        return { ...msg, replies: addReplyToMessages(msg.replies, parentId, reply) };
      }
      return msg;
    });
  };

  const handlePostReply = async (messageId: string) => {
    if (!id || !replyText.trim()) return;
    try {
      const token = getGoogleToken();
      await feedbackService.postFeedback(
        id,
        { content: replyText.trim(), parentId: messageId },
        token
      );
      
      // Refresh feedback from public API after posting
      const refreshedFeedback = await feedbackService.getPublicFeedback(id);
      
      // Process the feedback data
      const byId: Record<string, any> = {};
      const topLevel: any[] = [];

      refreshedFeedback.forEach((fb: any) => {
        byId[fb.id] = {
          id: fb.id,
          name: fb.authorName ?? fb.name ?? 'Supporter',
          email: fb.authorEmail ?? fb.email ?? '',
          message: fb.content ?? fb.message ?? '',
          date: formatDateTime(fb.createdAt),
          replies: [] as any[],
        };
      });

      refreshedFeedback.forEach((fb: any) => {
        if (fb.parentId && byId[fb.parentId]) {
          byId[fb.parentId].replies.push(byId[fb.id]);
        } else if (!fb.parentId) {
          topLevel.push(byId[fb.id]);
        }
      });
      
      setSupportMessages(topLevel);
      setReplyText('');
      setReplyingTo(null);
      toast.success('Reply posted!');
    } catch (err) {
      console.error('Error posting reply:', err);
      const message = (err as any)?.response?.data?.message || (err as Error)?.message || 'Failed to post reply';
      setErrorDetails({ message });
      setStatus('error');
    }
  };

  const handleDeleteMessage = async (messageId: string) => {
    if (!id || !isAuthenticated) return;
    const token = getGoogleToken();
    if (!token) {
      toast.error('Authentication required to delete messages');
      return;
    }
    
    try {
      // Find the message to verify ownership before deletion
      const message = supportMessages.find(msg => msg.id === messageId);
      if (!message) {
        toast.error('Message not found');
        return;
      }
      
      // Verify the current user is the author
      const isAuthor = isCurrentUserAuthor(message.authorName || message.name) || 
                      (getCurrentUserEmail() && message.email && 
                       getCurrentUserEmail()?.toLowerCase() === message.email.toLowerCase());
      
      console.log('Delete authorization check:', {
        messageId: message.id,
        name: message.name || message.authorName,
        isAuthor
      });
      
      if (!isAuthor) {
        toast.error('You can only delete your own messages');
        return;
      }
      
      await feedbackService.deleteFeedback(messageId, token);
      
      // Refresh feedback from public API after deleting
      await fetchPublicFeedback(id);
      toast.success('Message deleted');
    } catch (err) {
      console.error('Error deleting message:', err);
      const message = (err as any)?.response?.data?.message || (err as Error)?.message || 'Failed to delete message';
      setErrorDetails({ message });
      setStatus('error');
    }
  };

  const handleDeleteReply = async (parentId: string, replyId: string) => {
    if (!id || !isAuthenticated) return;
    const token = getGoogleToken();
    if (!token) {
      toast.error('Authentication required to delete replies');
      return;
    }
    
    try {
      // Find the parent message and reply to verify ownership before deletion
      const parentMessage = supportMessages.find(msg => msg.id === parentId);
      if (!parentMessage || !parentMessage.replies) {
        toast.error('Parent message not found');
        return;
      }
      
      const reply = parentMessage.replies.find(r => r.id === replyId);
      if (!reply) {
        toast.error('Reply not found');
        return;
      }
      
      // Verify the current user is the author of the reply
      const isAuthor = isCurrentUserAuthor(reply.authorName || reply.name) || 
                      (getCurrentUserEmail() && reply.email && 
                       getCurrentUserEmail()?.toLowerCase() === reply.email.toLowerCase());
      
      if (!isAuthor) {
        toast.error('You can only delete your own replies');
        return;
      }
      
      await feedbackService.deleteFeedback(replyId, token);
      
      // Refresh feedback from public API after deleting
      await fetchPublicFeedback(id);
      toast.success('Reply deleted');
    } catch (err) {
      console.error('Error deleting reply:', err);
      const message = (err as any)?.response?.data?.message || (err as Error)?.message || 'Failed to delete reply';
      setErrorDetails({ message });
      setStatus('error');
    }
  };

  const calculateDaysLeft = (endDate: string) => {
    const end = new Date(endDate);
    const now = new Date();
    const diffTime = end.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays > 0 ? diffDays : 0;
  };

  useEffect(() => {
    if (!id) return;

    const fetchCampaign = async () => {
      try {
        const data = await supporterService.getCampaignById(id);
        setCampaign(data);
      } catch (error) {
        console.error('Error fetching campaign:', error);
        const message = (error as any)?.response?.data?.message || (error as Error)?.message || 'Failed to load campaign';
        setErrorDetails({ message });
        setStatus('error');
        if (process.env.NODE_ENV === 'development') {
          setCampaign(mockCampaignDetails);
        }
      }
    };

    fetchCampaign();
  }, [id]);

  // Translation effect
  useEffect(() => {
    const translateContent = async () => {
      if (!campaign) return;
      
      try {
        // Translate campaign details and UI labels for all users
        const promises = [
          translationService.translateCampaignDetails({
            campaignTitle: campaign.campaign.name,
            description: campaign.campaign.story
          }, selectedLanguage),
          translationService.translateUILabels(selectedLanguage)
        ];
        
        // Translate support messages if there are any (for all users)
        if (supportMessages.length > 0) {
          promises.push(translationService.translateSupportMessages(supportMessages, selectedLanguage));
        }
        
        const results = await Promise.all(promises);
        const translatedCampaignData = results[0];
        const labels = results[1];
        const translatedMsgs = results.length > 2 ? results[2] : supportMessages; // Default to original messages if not translated
        
        setTranslatedCampaign({
          ...campaign,
          campaign: {
            ...campaign.campaign,
            name: translatedCampaignData.campaignTitle || campaign.campaign.name,
            story: translatedCampaignData.description || campaign.campaign.story
          }
        });
        setUiLabels(labels);
        setTranslatedMessages(translatedMsgs);
      } catch (error) {
        console.error('Translation error:', error);
        setTranslatedCampaign(campaign);
        setUiLabels({});
        setTranslatedMessages(supportMessages);
      }
    };

    translateContent();
  }, [campaign, selectedLanguage, supportMessages]);

  // Function to fetch feedback using the public API
  const fetchPublicFeedback = async (campaignId: string) => {
    try {
      // Always use public API to fetch feedback without authentication
      const feedbackList = await feedbackService.getPublicFeedback(campaignId);

      const byId: Record<string, any> = {};
      const topLevel: any[] = [];

      feedbackList.forEach((fb: any) => {
        byId[fb.id] = {
          id: fb.id,
          name: fb.authorName ?? fb.name ?? 'Supporter',
          email: fb.authorEmail ?? fb.email ?? '',
          message: fb.content ?? fb.message ?? '',
          date: formatDateTime(fb.createdAt),
          replies: [] as any[],
        };
      });

      feedbackList.forEach((fb: any) => {
        if (fb.parentId && byId[fb.parentId]) {
          byId[fb.parentId].replies.push(byId[fb.id]);
        } else if (!fb.parentId) {
          topLevel.push(byId[fb.id]);
        }
      });

      setSupportMessages(topLevel);
    } catch (err) {
      console.error('Error fetching feedback:', err);
    }
  };

  // Fetch feedback when component mounts or campaign ID changes
  useEffect(() => {
    if (!id) return;
    fetchPublicFeedback(id);
  }, [id]);

  if (status === 'success') {
    return (
      <StatusPage
        type="success"
        title="Payment Successful!"
        message={`Thank you for your generous donation of ₹${donationAmount}. Your support will make a real difference in ${campaign?.name || 'this student'}'s education.`}
        actionText="View My Donations"
        backUrl={`/supporters?tab=donations`}
      />
    );
  }

  if (status === 'pending') {
    return (
      <StatusPage
        type="pending"
        title="Payment Status Unclear"
        message={errorDetails?.message || 'We are unable to confirm your payment status at this time. This could be due to:\n\n• Payment is still being processed by your bank\n• Network connectivity issues\n• Payment was incomplete\n\nPlease check your bank statement or payment history. If money was deducted, contact support with your transaction details.'}
        actionText="Check My Donations"
        backUrl={`/supporters?tab=donations`}
        secondaryActionText="Try Again"
        onSecondaryAction={() => { setStatus('idle'); setErrorDetails(null); }}
      />
    );
  }

  if (status === 'error') {
    return (
      <StatusPage
        type="error"
        title="Error"
        message={errorDetails?.message || ''}
        actionText="Try Again"
        onAction={() => { setStatus('idle'); setErrorDetails(null); }}
      />
    );
  }

  if (!campaign) {
    return <div>Loading...</div>;
  }

  const progress = Math.round((campaign.campaign.raisedAmount / campaign.campaign.targetAmount) * 100);
  const daysLeft = calculateDaysLeft(campaign.campaign.endDate);

  const renderShareModal = () => (
    showShareModal && (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-[70]">
        <div className="bg-white rounded-lg p-6 max-w-md w-full">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-xl font-semibold">Share Campaign</h3>
            <button onClick={() => setShowShareModal(false)} className="text-gray-500">
              <X className="h-6 w-6" />
            </button>
          </div>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <input
                type="text"
                readOnly
                value={window.location.href}
                className="flex-1 bg-transparent outline-none"
              />
              <button
                onClick={() => {
                  navigator.clipboard.writeText(window.location.href);
                  toast.success('Link copied to clipboard!');
                }}
                className="ml-2 text-blue-600 hover:text-blue-800"
              >
                Copy
              </button>
            </div>
            {/* <div className="grid grid-cols-2 gap-4">
              <button className="flex items-center justify-center p-3 bg-[#1DA1F2] text-white rounded-lg hover:bg-[#1a8cd8]">
                Share on Twitter
              </button>
              <button className="flex items-center justify-center p-3 bg-[#4267B2] text-white rounded-lg hover:bg-[#365899]">
                Share on Facebook
              </button>
            </div> */}
          </div>
        </div>
      </div>
    )
  );

  // const renderQRModal = () => (
  //   showQRModal && (
  //     <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-[70]">
  //       <div className="bg-white rounded-lg p-6 max-w-md w-full">
  //         <div className="flex justify-between items-center mb-4">
  //           <h3 className="text-xl font-semibold">Campaign QR Code</h3>
  //           <button onClick={() => setShowQRModal(false)} className="text-gray-500">
  //             <X className="h-6 w-6" />
  //           </button>
  //         </div>
  //         <div className="flex flex-col items-center space-y-4">
  //           <div className="bg-gray-100 p-4 rounded-lg">
  //             <div className="w-48 h-48 bg-white border-2 border-gray-300 rounded-lg flex items-center justify-center">
  //               <QrCode className="w-32 h-32 text-gray-400" />
  //             </div>
  //           </div>
  //           <button
  //             onClick={() => {
  //               toast.success('QR Code downloaded!');
  //             }}
  //             className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
  //           >
  //             Download QR Code
  //           </button>
  //         </div>
  //       </div>
  //     </div>
  //   )
  // );

  const loadRazorpay = () => {
    return new Promise((resolve) => {
      const script = document.createElement('script');
      script.src = 'https://checkout.razorpay.com/v1/checkout.js';
      script.onload = () => {
        resolve(true);
      };
      script.onerror = () => {
        resolve(false);
      };
      document.body.appendChild(script);
    });
  };

  const handlePaymentComplete = (success: boolean) => {
    setShowPaymentModal(false);
    if (success) {
      setStatus('success');
    } else {
      setStatus('error');
      setErrorDetails({ message: 'Payment failed. Please try again.' });
      setDonationAmount('');
    }
  };

  const initializeRazorpayPayment = async () => {
    try {
      const res = await loadRazorpay();
      if (!res) {
        toast.error('Razorpay SDK failed to load');
        return;
      }
      if (!campaign) {
        toast.error('No campaign selected');
        return;
      }

      const amountValue = Number(donationAmount);
      if (isNaN(amountValue) || amountValue <= 0) {
        toast.error('Please enter a valid donation amount');
        return;
      }

      const order = await paymentService.createOrder(String(campaign.id), amountValue);

      const options = {
        key: paymentService.getRazorpayKey(),
        amount: order.amount,
        currency: 'INR',
        name: 'EDU-FUND',
        description: `Supporting ${campaign?.name}'s education`,
        image: 'https://img.icons8.com/?size=100&id=51z5mmzrVhk8&format=png&color=000000',
        order_id: order.orderId,
        handler: async function (response: any) {
          try {
            // Check if we have all required payment data
            if (response.razorpay_payment_id && response.razorpay_order_id && response.razorpay_signature) {
              // We have complete payment data - verify with backend
              try {
                const result = await paymentService.verifyPayment(response);
                if (result.success) {
                  // Backend confirmed payment is successful
                  handlePaymentComplete(true);
                } else {
                  // Backend says payment failed despite having payment ID
                  console.warn('Payment verification failed:', result.message);
                  setErrorDetails({ message: result.message || 'Payment verification failed' });
                  setStatus('error');
                }
              } catch (verifyErr: any) {
                // Backend verification failed - could be network issue
                console.error('Payment verification error:', verifyErr);
                // Show pending status and let user check manually
                setErrorDetails({ message: 'Payment status unclear. Please check your payment history or contact support.' });
                setStatus('pending');
              }
            } else if (response.razorpay_payment_id) {
              // We have payment ID but missing other data - incomplete payment
              console.log('Incomplete payment data received');
              setErrorDetails({ message: 'Payment is incomplete. Please try again or contact support.' });
              setStatus('pending');
            } else {
              // No payment ID at all - payment not initiated properly
              console.log('No payment ID received - payment not completed');
              setErrorDetails({ message: 'Payment was not completed. Please try again.' });
              setStatus('error');
            }
          } catch (err: any) {
            console.error('Payment processing failed', err);
            setErrorDetails({ message: err.message || 'Payment processing failed' });
            setStatus('error');
          }
        },
        prefill: {
          name: 'Supporter Name',
          email: '<EMAIL>',
          contact: '9999999999',
        },
        notes: {
          campaignId: campaign?.id ?? '',
          studentName: campaign?.name ?? '',
          institution: campaign?.institution ?? '',
        },
        theme: {
          color: '#2563EB',
        },
        modal: {
          ondismiss: function () {
            handlePaymentComplete(false);
          },
        },
      } as any;

      const paymentObject = new (window as any).Razorpay(options);

      paymentObject.on('payment.failed', function(response: any) {
        const description = response?.error?.description;
        toast.error(`Payment failed: ${description}`);
        setErrorDetails({ message: description });
        setStatus('error');
        handlePaymentComplete(false);
      });
      paymentObject.open();
    } catch (error: any) {
      console.error('Payment initialization error:', error);
      const apiMessage = (error as any)?.response?.data?.message;
      const apiDescription = (error as any)?.response?.data?.error?.description;
      const message = apiDescription || apiMessage || (error as Error)?.message;
      setErrorDetails({ message });
      setStatus('error');
      handlePaymentComplete(false);
    }
  };

  const handleProceedToPayment = () => {
    if (!donationAmount || Number(donationAmount) <= 0) {
      toast.error('Please enter a valid donation amount');
      return;
    }

    if (Number(donationAmount) > 500000) {
      toast.error('Maximum donation amount is ₹5,00,000');
      return;
    }

    initializeRazorpayPayment();
  };

  const renderPaymentModal = () => (
    showPaymentModal && (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-[70]">
        <div className="bg-white rounded-lg p-6 max-w-md w-full">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-xl font-semibold">Support Campaign</h3>
            <button onClick={() => setShowPaymentModal(false)} className="text-gray-500">
              <X className="h-6 w-6" />
            </button>
          </div>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Select Amount (₹)
              </label>
              <div className="grid grid-cols-2 gap-2">
                {[1000, 2000, 5000, 10000].map((amount) => (
                  <button
                    key={amount}
                    onClick={() => setDonationAmount(amount.toString())}
                    className={`p-2 rounded border ${
                      donationAmount === amount.toString()
                        ? 'bg-blue-600 text-white'
                        : 'hover:bg-gray-50'
                    }`}
                  >
                    ₹{amount}
                  </button>
                ))}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Or Enter Custom Amount
              </label>
              <div className="relative">
                <span className="absolute left-3 top-2">₹</span>
                <input
                  type="number"
                  value={donationAmount}
                  onChange={(e) => setDonationAmount(e.target.value)}
                  className="pl-8 p-2 border rounded w-full"
                  placeholder="Enter amount"
                />
              </div>
            </div>

            <button
              onClick={handleProceedToPayment}
              className="w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700"
            >
              Proceed to Payment
            </button>
          </div>
        </div>
      </div>
    )
  );

  const renderReceiptModal = () => (
    showReceiptModal && (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-[70]">
        <div className="bg-white rounded-lg p-6 max-w-2xl w-full">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-xl font-semibold">Payment Receipt</h3>
            <button onClick={() => setShowReceiptModal(false)} className="text-gray-500">
              <X className="h-6 w-6" />
            </button>
          </div>
          <div className="space-y-6">
            <div className={`p-4 rounded-lg ${paymentStatus === 'success' ? 'bg-green-50' : 'bg-red-50'}`}>
              <div className="flex items-center">
                {paymentStatus === 'success' ? (
                  <CheckCircle className="h-8 w-8 text-green-500 mr-3" />
                ) : (
                  <XCircle className="h-8 w-8 text-red-500 mr-3" />
                )}
                <div>
                  <h4 className="text-lg font-semibold">
                    {paymentStatus === 'success' ? 'Payment Successful!' : 'Payment Failed'}
                  </h4>
                  <p className="text-sm text-gray-600">
                    {paymentStatus === 'success'
                      ? 'Thank you for supporting this campaign.'
                      : 'There was an issue processing your payment.'}
                  </p>
                </div>
              </div>
            </div>

            {paymentStatus === 'success' && (
              <>
                <div className="border-t border-b py-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-gray-500">Campaign</p>
                      <p className="font-medium">{campaign?.campaign.name}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Amount</p>
                      <p className="font-medium">₹{Number(donationAmount).toLocaleString()}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Student</p>
                      <p className="font-medium">{campaign?.name}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Institution</p>
                      <p className="font-medium">{campaign?.institution}</p>
                    </div>
                  </div>
                </div>

                <div className="flex justify-end space-x-4">
                  <button
                    onClick={() => setShowReceiptModal(false)}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                  >
                    Close
                  </button>
                </div>
              </>
            )}

            {paymentStatus === 'failed' && (
              <div className="flex justify-end space-x-4">
                <button
                  onClick={() => {
                    setShowReceiptModal(false);
                    setShowPaymentModal(true);
                  }}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                  Try Again
                </button>
                <button
                  onClick={() => setShowReceiptModal(false)}
                  className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"
                >
                  Close
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    )
  );

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      <SupporterHeader />

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 flex-grow">
        {/* Language Dropdown */}
        <div className="flex justify-end mb-4">
          <LanguageDropdown
            selectedLanguage={selectedLanguage}
            onLanguageChange={setSelectedLanguage}
          />
        </div>
        
        {/* Breadcrumb */}
        <Breadcrumb
          items={getBreadcrumbItems('supporter-campaign-details', {
            campaignTitle: translatedCampaign?.campaign.name || campaign.campaign.name,
            campaignId: campaign.id.toString()
          })}
          className="mb-6"
        />

        {/* Campaign Status */}
        <div className="mb-6">
          <div className="flex items-center gap-4 mb-2">
            <span className="px-3 py-1 bg-green-100 text-green-800 text-sm font-medium rounded-full">
              Active
            </span>
            <div className="flex items-center text-gray-600 text-sm">
              <Calendar className="h-4 w-4 mr-1" />
              <span>Campaign Duration: {new Date(campaign.campaign.startDate).toLocaleDateString()} - {new Date(campaign.campaign.endDate).toLocaleDateString()}</span>
            </div>
          </div>
          <h1 className="text-3xl font-bold mb-8">{translatedCampaign?.campaign.name || campaign.campaign.name}</h1>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Campaign Stats */}
          <div className="lg:col-span-2">
            <div className="grid grid-cols-4 gap-4 mb-8">
              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="text-sm text-gray-600">{uiLabels.targetAmount || 'Target'}</div>
                <div className="text-xl font-semibold">₹{campaign.campaign.targetAmount.toLocaleString()}</div>
              </div>
              <div className="bg-green-50 p-4 rounded-lg">
                <div className="text-sm text-gray-600">{uiLabels.raised || 'Raised'}</div>
                <div className="text-xl font-semibold">₹{campaign.campaign.raisedAmount.toLocaleString()}</div>
              </div>
              <div className="bg-purple-50 p-4 rounded-lg">
                <div className="text-sm text-gray-600">{uiLabels.supporters || 'Supporters'}</div>
                <div className="text-xl font-semibold">{campaign.campaign.supporters}</div>
              </div>
              <div className="bg-orange-50 p-4 rounded-lg">
                <div className="text-sm text-gray-600">Days Left</div>
                <div className="text-xl font-semibold">{daysLeft}</div>
              </div>
            </div>

            {/* Student Information */}
            <div className="bg-white rounded-lg p-6 shadow-sm mb-6">
              <h2 className="text-xl font-semibold mb-4">{uiLabels.studentDetails || 'Student Information'}</h2>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <div className="text-sm text-gray-600">Institution</div>
                  <div className="flex items-center space-x-3">
                    {campaign.institutionLogo && (
                      <img 
                        src={campaign.institutionLogo} 
                        alt={`${campaign.institution} logo`}
                        className="w-8 h-8 object-contain rounded"
                        onError={(e) => {
                          e.currentTarget.style.display = 'none';
                        }}
                      />
                    )}
                    <div className="font-medium">{campaign.institution}</div>
                  </div>
                </div>
                <div>
                  <div className="text-sm text-gray-600">Course</div>
                  <div className="font-medium">{campaign.course}</div>
                </div>
                <div>
                  <div className="text-sm text-gray-600">Email</div>
                  <div className="font-medium">{campaign.email}</div>
                </div>
                <div>
                  <div className="text-sm text-gray-600">Phone</div>
                  <div className="font-medium">{campaign.phone}</div>
                </div>
              </div>
            </div>

            {/* Campaign Description */}
            <div className="bg-white rounded-lg p-6 shadow-sm mb-6">
              <h2 className="text-xl font-semibold mb-4">{uiLabels.description || 'Campaign Description'}</h2>
              <div className="prose max-w-none">
                <p className="whitespace-pre-line">{translatedCampaign?.campaign.story || campaign.campaign.story}</p>
              </div>
            </div>

            {/* Words of Support Section - Shown to all users */}
            <div className="bg-white rounded-lg p-6 shadow-sm mb-6">
              <h2 className="text-xl font-semibold mb-4">{uiLabels.wordsOfSupport || 'Words of Support'}</h2>
              
              {/* Add Support Message - Only shown when authenticated */}
              {isAuthenticated ? (
                <div className="mb-6">
                  <textarea
                    value={wordsOfSupport}
                    onChange={(e) => setWordsOfSupport(e.target.value)}
                    placeholder={uiLabels.supportPlaceholder || 'Share your words of encouragement and support...'}
                    className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    rows={3}
                    maxLength={500}
                  />
                  <div className="flex justify-between items-center mt-2">
                    <span className="text-sm text-gray-500">{wordsOfSupport.length}/500 characters</span>
                    <button
                      onClick={async () => {
                        if (!id || !wordsOfSupport.trim()) return;
                        try {
                          const token = getGoogleToken();
                          await feedbackService.postFeedback(
                            id,
                            { content: wordsOfSupport.trim(), parentId: null },
                            token
                          );
                          
                          // Refresh feedback from public API after posting
                          await fetchPublicFeedback(id);
                          setWordsOfSupport('');
                          toast.success('Your words of support have been shared!');
                        } catch (err) {
                          console.error('Error posting feedback:', err);
                          toast.error('Please enter your message of support');
                        }
                      }}
                      disabled={!wordsOfSupport.trim()}
                      className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
                    >
                      {uiLabels.share || 'Share Support'}
                    </button>
                  </div>
                </div>
              ) : (
                <div className="mb-6 bg-blue-50 border-l-4 border-blue-400 p-4 rounded-md">
                  <div className="flex">
                    <div className="ml-3">
                      <p className="text-sm text-blue-700">
                        <button 
                          onClick={() => {
                            sessionStorage.setItem('login_redirect', window.location.pathname);
                            navigate('/supporter-login');
                          }}
                          className="font-medium underline"
                        >
                          Sign in
                        </button> to share your words of support for this student.
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {/* Support Messages */}
              <div className="space-y-4">
                {translatedMessages.length > 0 ? (
                  translatedMessages.map((message) => {
                    // Debug the message data
                    console.log('Message data:', {
                      id: message.id,
                      name: message.name,
                      authorName: message.authorName,
                      email: message.email,
                      authorEmail: message.authorEmail,
                      content: message.message
                    });
                    
                    // Check if the current user is the author of the comment
                    const canDeleteMessage = isAuthenticated && (
                      isCurrentUserAuthor(message.authorName || message.name) ||
                      (getCurrentUserEmail() && message.email && 
                       getCurrentUserEmail()?.toLowerCase() === message.email.toLowerCase())
                    );
                    
                    // Debug the authorization result
                    console.log('Delete authorization for message:', {
                      messageId: message.id,
                      name: message.name || message.authorName,
                      canDelete: canDeleteMessage,
                      currentUserEmail: getCurrentUserEmail(),
                      currentUserName: getCurrentUserName()
                    });
                    
                    return (
                    <div key={message.id} className="bg-gray-50 p-4 rounded-lg">
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <p className="font-medium text-gray-900">{message.name}</p>
                          {/* Don't display email for privacy reasons */}
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className="text-sm text-gray-500">{message.date}</span>
                          {/* Show delete button if the user is the author */}
                          {isAuthenticated && (
                            <button
                              onClick={() => handleDeleteMessage(message.id)}
                              className="text-red-600 hover:text-red-800"
                              title="Delete message"
                              // Force display for ICamXperts Org comments
                              style={{ display: canDeleteMessage ? 'block' : 'none' }}
                            >
                              <Trash2 className="h-4 w-4" />
                            </button>
                          )}
                        </div>
                      </div>
                      <p className="text-gray-700 mb-3">{message.message}</p>
                      
                      {/* Reply Button */}
                      {isAuthenticated ? (
                        <button
                          onClick={() => setReplyingTo(replyingTo === message.id ? null : message.id)}
                          className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                        >
                          {replyingTo === message.id ? 'Cancel Reply' : 'Reply'}
                        </button>
                      ) : (
                        <button
                          onClick={() => {
                            sessionStorage.setItem('login_redirect', window.location.pathname);
                            navigate('/supporter-login');
                          }}
                          className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                        >
                          Login to Reply
                        </button>
                      )}
                      
                      {/* Reply Input */}
                      {replyingTo === message.id && (
                        <div className="mt-3 pl-4 border-l-2 border-blue-200">
                          <textarea
                            value={replyText}
                            onChange={(e) => setReplyText(e.target.value)}
                            placeholder="Write your reply..."
                            className="w-full p-2 border border-gray-300 rounded text-sm"
                            rows={2}
                            maxLength={300}
                          />
                          <div className="flex justify-between items-center mt-2">
                            <span className="text-xs text-gray-500">{replyText.length}/300</span>
                            <button
                              onClick={() => handlePostReply(message.id)}
                              disabled={!replyText.trim()}
                              className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 disabled:bg-gray-400"
                            >
                              Post Reply
                            </button>
                          </div>
                        </div>
                      )}
                      
                      {/* Replies */}
                      {message.replies && message.replies.length > 0 && (
                        <div className="mt-4 pl-4 border-l-2 border-gray-200 space-y-3">
                          {message.replies.map((reply) => {
                            // Check if the current user is the author of the reply
                            const canDeleteReply = isAuthenticated && (
                              // Check by author name
                              isCurrentUserAuthor(reply.authorName || reply.name) ||
                              // Check by email if available
                              (getCurrentUserEmail() && reply.email && 
                               getCurrentUserEmail()?.toLowerCase() === reply.email.toLowerCase())
                            );
                            return (
                            <div key={reply.id} className="bg-white p-3 rounded">
                              <div className="flex justify-between items-start mb-1">
                                <div>
                                  <p className="font-medium text-sm text-blue-600">{reply.name}</p>
                                  {/* Don't display email for privacy reasons */}
                                </div>
                                <div className="flex items-center space-x-2">
                                  <span className="text-xs text-gray-500">{reply.date}</span>
                                  {/* Show delete button if the user is the author */}
                                  {isAuthenticated && (
                                    <button
                                      onClick={() => handleDeleteReply(message.id, reply.id)}
                                      className="text-red-600 hover:text-red-800"
                                      title="Delete reply"
                                      style={{ display: canDeleteReply ? 'block' : 'none' }}
                                    >
                                      <Trash2 className="h-4 w-4" />
                                    </button>
                                  )}
                                </div>
                              </div>
                              <p className="text-gray-700 text-sm">{reply.message}</p>
                            </div>
                            );
                          })}
                        </div>
                      )}
                    </div>
                    );
                  })
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <p>{uiLabels.noMessages || 'No words of support yet. Be the first to encourage this student!'}</p>
                  </div>
                )}
              </div>
            </div>

            {/* Supporters List */}
            {campaign.showSupportersToOthers && campaign.supportersList && campaign.supportersList.length > 0 && (
              <div className="bg-white rounded-lg p-6 shadow-sm mb-6">
                <h2 className="text-xl font-semibold mb-4">Recent Supporters</h2>
                <div className="space-y-4">
                  {campaign.supportersList.slice(0, 5).map((supporter) => (
                    <div key={supporter.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div>
                        <p className="font-medium text-gray-900">{supporter.name}</p>
                        <p className="text-sm text-gray-500">{new Date(supporter.date).toLocaleDateString()}</p>
                        {supporter.message && (
                          <p className="text-sm text-gray-600 mt-1 italic">"{supporter.message}"</p>
                        )}
                      </div>
                      <div className="text-right">
                        <p className="font-semibold text-blue-600">₹{supporter.amount.toLocaleString()}</p>
                      </div>
                    </div>
                  ))}
                  {campaign.supportersList.length > 5 && (
                    <p className="text-sm text-gray-500 text-center pt-2">
                      and {campaign.supportersList.length - 5} more supporters
                    </p>
                  )}
                </div>
              </div>
            )}

            {/* Campaign Updates section removed */}

            {/* Documents */}
            {/* {campaign.campaign.documents.length > 0 && (
              <div className="bg-white rounded-lg p-6 shadow-sm">
                <h2 className="text-xl font-semibold mb-4">Supporting Documents</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {campaign.campaign.documents.map((doc, index) => (
                    <div key={index} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                      <FileText className="h-5 w-5 text-gray-500" />
                      <span className="flex-1 truncate">{doc.name}</span>
                      <div className="flex space-x-2">
                        <a href={doc.file} className="text-blue-600 hover:text-blue-800" target="_blank" rel="noopener noreferrer">
                          View
                        </a>
                        <button
                          className="text-blue-600 hover:text-blue-800"
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();

                            // Create a mock document for download
                            const mockFileName = doc.name.replace(/\s+/g, '_').toLowerCase() + '.pdf';
                            const blob = new Blob(['Mock document content for ' + doc.name], { type: 'application/pdf' });
                            const url = URL.createObjectURL(blob);

                            // Create a temporary link and trigger download
                            const a = document.createElement('a');
                            a.href = url;
                            a.download = mockFileName;
                            document.body.appendChild(a);
                            a.click();

                            // Clean up
                            setTimeout(() => {
                              document.body.removeChild(a);
                              URL.revokeObjectURL(url);
                            }, 100);

                            toast.success(`Downloaded ${doc.name}`);
                          }}
                        >
                          Download
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )} */}
          </div>

          {/* Right Column - Progress */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg p-6 shadow-sm sticky top-4">
              <div className="mb-4">
                <div className="flex justify-between mb-2">
                  <span className="text-gray-600">Raised so far</span>
                  <span className="font-semibold text-blue-600">₹{campaign.campaign.raisedAmount.toLocaleString()}</span>
                </div>
                <div className="w-full h-2.5 bg-gray-200 rounded-full">
                  <div
                    className="bg-blue-600 h-2.5 rounded-full transition-all duration-500 ease-in-out"
                    style={{ width: `${progress}%` }}
                  />
                </div>
                <div className="flex justify-between mt-2 text-sm">
                  <span className="text-gray-500">Goal: ₹{campaign.campaign.targetAmount.toLocaleString()}</span>
                  <span className="text-blue-600">{progress}%</span>
                </div>
              </div>
              <button
                onClick={() => {
                  if (!isAuthenticated) {
                    // Store the current campaign URL to redirect back after login
                    sessionStorage.setItem('login_redirect', window.location.pathname);
                    navigate('/supporter-login');
                  } else {
                    setShowPaymentModal(true);
                  }
                }}
                className="w-full bg-blue-600 text-white py-3 rounded-lg hover:bg-blue-700 font-medium mb-3"
              >
                {isAuthenticated ? (uiLabels.supportStudent || 'Support this Campaign') : 'Support this Campaign'}
              </button>
              
              <button
                onClick={() => setShowShareModal(true)}
                className="w-full flex items-center justify-center space-x-2 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 font-medium"
              >
                <Share2 className="h-4 w-4" />
                <span>Share Campaign</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Modals */}
      {renderShareModal()}
      {/* {renderQRModal()} */}
      {renderPaymentModal()}
     
    </div>
  );
}



