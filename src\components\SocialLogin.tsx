
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { DevGoogleLogin } from './DevGoogleLogin';
import externalStudentService from '../services/externalStudentService';
import externalUserService from '../services/externalUserService';
import { useStudentAuth } from '../context/StudentAuthContext';
import { useSupporterAuth } from '../context/SupporterAuthContext';
import supporterService from '../services/supporterService';
import StatusPage from './StatusPage';

interface SocialLoginProps {
  userType: 'student' | 'supporter';
}

export function SocialLogin({ userType }: SocialLoginProps) {
  const { login: studentLogin } = useStudentAuth();
  const { login: supporterLogin } = useSupporterAuth();
  const navigate = useNavigate();

  const [status, setStatus] = useState<'idle' | 'error'>('idle');
  const [errorDetails, setErrorDetails] = useState<{ message?: string } | null>(null);

  // Combined handler for both sign in and sign up
  const handleGoogleContinue = async (credentialResponse: any) => {
    try {
      console.log('Google response:', credentialResponse);

      // Real Google OAuth returns a credential we can use
        if (!credentialResponse.credential) {
          console.error('No credential found in Google response');
          setErrorDetails({ message: 'Google login failed. Please try again.' });
          setStatus('error');
          return;
        }

      const credential = credentialResponse.credential;
      console.log("Google credential (ID token):", credential);

      // Use secure in-memory storage instead of localStorage
      const { setGoogleToken } = await import('../services/googleApi');
      setGoogleToken(credential);


      // Decode the JWT token from Google
      try {
        const decoded = JSON.parse(atob(credential.split('.')[1]));
        console.log('Decoded token:', decoded);

        const userData = {
          email: decoded.email,
          name: decoded.name,
          picture: decoded.picture,
        };





if (userType === 'student') {
  try {
    await studentLogin({ email: userData.email, password: 'dummy-password' }); // Use correct context login
    const check = await externalStudentService.checkLoggedInStudent();
    if (check.exists && check.status === 'ACTIVE' || check.status === 'INVITED') {
      toast.success('Welcome back!');
      navigate('/students');
    } else {
       if (check.exists && check.status !== 'ACTIVE' && check.status !== 'INVITED') {
        toast.info('Your student account is not active yet.');
      }
      localStorage.setItem('registration_prefill', JSON.stringify(userData));
      localStorage.setItem('google_signup_email', userData.email);
      navigate('/student-registration');
    }
  } catch (error) {
    console.error('Login or existence check failed:', error);
    setErrorDetails({ message: 'Login failed or user not verified' });
    setStatus('error');
  }
}

 else {
        // For supporters, use the new API-based flow
        try {
          await supporterLogin({ email: userData.email, password: 'dummy-password' });
          const check = await supporterService.checkLoggedInSupporter();
          if (check.exists) {
            // Store supporter email for easier access during sign-out
            localStorage.setItem('supporter_email', userData.email);
            localStorage.setItem('google_auth_picture', userData.picture);
            localStorage.setItem('google_auth_name', userData.name);

            toast.success('Welcome back!');

            // Check if there's a redirect URL stored in session storage
            const redirectUrl = sessionStorage.getItem('login_redirect');
            if (redirectUrl) {
              console.log('Redirecting to:', redirectUrl);
              // Clear the stored URL to prevent future unwanted redirects
              sessionStorage.removeItem('login_redirect');
              // Check if the redirect URL already contains the base path
              const basePath = import.meta.env.VITE_BASE_PATH || '';
              let fullPath = redirectUrl;

              // Only add base path if it's not already included
              if (basePath && !redirectUrl.startsWith(basePath)) {
                fullPath = redirectUrl.startsWith('/') ? `${basePath}${redirectUrl}` : `${basePath}/${redirectUrl}`;
              }

              // Use window.location.href for a full page reload to ensure proper path resolution
              window.location.href = fullPath;
            } else {
              navigate('/supporters');
            }
          } else {
            // First time supporter - navigate to registration form
            localStorage.setItem('supporter_registration_prefill', JSON.stringify(userData));
            localStorage.setItem('supporter_email', userData.email);
            navigate('/supporter-registration-form');
          }
        } catch (error) {
          console.error('Supporter login or existence check failed:', error);
          setErrorDetails({ message: 'Login failed or user not verified' });
          setStatus('error');
        }
      }
    } catch (error) {
      console.error('Google login error:', error);
      setErrorDetails({ message: 'Google login failed. Please try again.' });
      setStatus('error');
    }
      } catch (error) {
        console.error('Error decoding Google token:', error);
        setErrorDetails({ message: 'Failed to process Google login. Please try again.' });
        setStatus('error');
      }
  };

  if (status === 'error') {
    return (
      <StatusPage
        type="error"
        title="Error"
        message={errorDetails?.message || ''}
        actionText="Try Again"
        onAction={() => {
          setStatus('idle');
          setErrorDetails(null);
        }}
      />
    );
  }

  // Render different UI based on user type
  if (userType === 'supporter') {
    return (
      <div className="mt-8">
        <div className="flex flex-col items-center">
          <div className="w-full max-w-md">
            <div className="bg-white shadow-md rounded-lg p-6 border border-gray-200 hover:shadow-lg transition-shadow duration-300">
              <h3 className="text-center text-lg font-medium text-gray-700 mb-4">Continue with</h3>
              <div className="transform hover:scale-105 transition-transform duration-300">
                <DevGoogleLogin
                  buttonText="Google"
                  onSuccess={handleGoogleContinue}
                  onError={() => {
                    setErrorDetails({ message: 'Google login failed. Please try again.' });
                    setStatus('error');
                  }}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // For students
  return (
    <div className="mt-6">
      <div className="space-y-4">
        <div>
          <DevGoogleLogin
            buttonText="Continue with Google"
            onSuccess={handleGoogleContinue}
            onError={() => {
              setErrorDetails({ message: 'Google login failed. Please try again.' });
              setStatus('error');
            }}
          />
        </div>
      </div>
    </div>
  );
}





