import React from 'react';
import { Login } from './Login';
import { Info } from 'lucide-react';

export function StudentLogin() {
  // Create a custom header component to inject into the Login component
  const StudentLoginHeader = () => (
    <div className="text-center mb-6">
      <h2 className="text-3xl font-bold mb-2">Welcome Back</h2>
      <p className="text-gray-600 mb-4">Sign in to your student account</p>
      
      <div className="bg-blue-50 border-l-4 border-blue-500 p-4 rounded-md text-left">
        <div className="flex">
          <div className="flex-shrink-0">
            <Info className="h-5 w-5 text-blue-500" />
          </div>
        <div className="ml-3">
  <h3 className="text-sm font-medium text-blue-800">Login Instructions</h3>
  <div className="mt-2 text-sm text-blue-700">
    <ul className="list-disc pl-5 space-y-1">
      <li>Use your student email address provided by your institution</li>
      <li>Click the "Sign in with Google" button to log in</li>
      <li>Make sure you're signed into your browser with your student email account</li>
      <li>If you're having trouble logging in, contact your institution's administrator</li>
    </ul>
  </div>
</div>

        </div>
      </div>
    </div>
  );

  return <Login userType="student" customHeader={<StudentLoginHeader />} />;
}