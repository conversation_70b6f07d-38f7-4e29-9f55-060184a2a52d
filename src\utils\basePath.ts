/**
 * Get the base path from environment variable
 * Uses VITE_BASE_PATH directly
 */
export const getBasePath = (): string => {
  const basePath = import.meta.env.VITE_BASE_PATH || '';
  // Remove any template literals that might be in the string
  return basePath.replace(/\$\{.*\}/g, '');
};

/**
 * Get the base path with trailing slash
 */
export const getBasePathWithSlash = (): string => {
  const basePath = getBasePath();
  return basePath.endsWith('/') ? basePath : `${basePath}/`;
};