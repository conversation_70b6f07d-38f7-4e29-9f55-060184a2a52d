import React, { useEffect, useState } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useSupporterAuth } from '../context/SupporterAuthContext';
import { hasGoogleToken } from '../services/googleApi';
import { toast } from 'react-toastify';
import Loading<PERSON>pinner from './LoadingSpinner';

interface SupporterProtectedRouteProps {
  children: React.ReactNode;
  redirectPath?: string;
}

/**
 * A route component that protects routes requiring supporter authentication
 * Redirects to the supporter login page if the user is not authenticated
 */
export function SupporterProtectedRoute({
  children,
  redirectPath = `${import.meta.env.VITE_BASE_PATH || ''}/supporter-login`
}: SupporterProtectedRouteProps) {
  const location = useLocation();
  const { isAuthenticated, user, loading } = useSupporterAuth();
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    const checkAuthentication = async () => {
      try {
        // Wait for the auth context to finish loading
        if (!loading) {
          // Check for Google auth or other specific supporter registrations
          const email = localStorage.getItem('supporter_email') || sessionStorage.getItem('supporter_email');
          const hasGoogleAuth = Object.keys(localStorage).some(key =>
            key.startsWith('supporter_registered_') ||
            key.includes('google') ||
            key.includes('oauth')
          );

          console.log('SupporterProtectedRoute - Authentication check:', {
            isAuthenticated,
            hasUser: !!user,
            pathname: location.pathname,
            hasGoogleAuth,
            email,
            localStorage: {
              token: localStorage.getItem('token') ? 'exists' : 'none',
              user: localStorage.getItem('user') ? 'exists' : 'none',
              supporterKeys: Object.keys(localStorage).filter(key => key.includes('supporter'))
            },
            sessionStorage: {
              token: sessionStorage.getItem('token') ? 'exists' : 'none',
              user: sessionStorage.getItem('user') ? 'exists' : 'none'
            }
          });

          setIsChecking(false);
        }
      } catch (error) {
        console.error('Error in supporter authentication check:', error);
        setIsChecking(false);
      }
    };

    checkAuthentication();
  }, [loading, isAuthenticated, user, location]);

  // Show loading state
  if (loading || isChecking) {
    return <LoadingSpinner message="Checking authentication..." />;
  }

  // Check for secure Google authentication and localStorage fallback
  const email = localStorage.getItem('supporter_email');
  const hasSecureGoogleAuth = hasGoogleToken(); // Secure in-memory token check
  const hasLocalStorageAuth = Object.keys(localStorage).some(key =>
    key.startsWith('supporter_registered_') ||
    key.includes('google_auth')
  );

  // Check if we have a valid session (either secure Google auth or localStorage fallback)
  const checkAuthSession = () => {
    // Priority 1: Secure Google token in memory
    if (hasSecureGoogleAuth) {
      console.log('Valid secure Google authentication found');
      return true;
    }

    // Priority 2: Regular auth context
    if (isAuthenticated) {
      console.log('Valid auth context authentication found');
      return true;
    }

    // Priority 3: localStorage fallback (for existing sessions)
    if (email && hasLocalStorageAuth) {
      console.log('Valid localStorage authentication found');
      return true;
    }

    // If we have email but no valid session, clear stale data
    if (email && !hasLocalStorageAuth && !hasSecureGoogleAuth) {
      console.log('Found supporter email but no valid session, clearing stale data');
      localStorage.removeItem('supporter_email');
      localStorage.removeItem('google_auth_picture');
      localStorage.removeItem('google_auth_name');

      // Clear any other Google auth-related items
      Object.keys(localStorage).forEach(key => {
        if (key.startsWith('supporter_registered_') ||
            key.includes('google') ||
            key.includes('oauth') ||
            key.includes('supporter')) {
          localStorage.removeItem(key);
        }
      });
    }

    return false;
  };

  const isValidSession = checkAuthSession();

  // If not authenticated, redirect to login
  if (!isValidSession) {
    console.log('Not authenticated as supporter, redirecting to', redirectPath, {
      isAuthenticated,
      hasSecureGoogleAuth,
      hasLocalStorageAuth,
      email,
      supporterKeys: Object.keys(localStorage).filter(key => key.includes('supporter'))
    });

    // Show a toast message
    toast.info('Please sign in to access this page');

    // Redirect to login page with the current location as the "from" state
    return <Navigate to={redirectPath} state={{ from: location }} replace />;
  }

  // If authenticated, render the protected content
  console.log('User is authenticated as supporter, rendering protected content');
  return <>{children}</>;
}

export default SupporterProtectedRoute;
